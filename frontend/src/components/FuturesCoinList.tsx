'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Search, RefreshCw, TrendingUp, TrendingDown, Activity, DollarSign, BarChart3, Zap, ArrowLeft } from 'lucide-react';
import { io, Socket } from 'socket.io-client';
import { getApiUrl, getWebSocketUrl, API_CONFIG } from '../utils/api';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import FuturesNotificationSystem from './FuturesNotificationSystem';
import FuturesEarlyWarningSystem from './FuturesEarlyWarningSystem';

export interface FuturesCoinListItem {
  symbol: string;
  name: string;
  price: number;
  priceChange24h: number;
  volume: number;
  marketCap?: number;
  lastUpdated: number;
  confidence: {
    [timeframe: string]: {
      action: 'BUY' | 'SELL' | 'HOLD';
      confidence: number;
      strength: number;
    };
  };
}

export interface FuturesCoinListStats {
  totalCoins: number;
  lastUpdate: number;
  marketSummary: {
    totalVolume: number;
    avgPriceChange: number;
    bullishCount: number;
    bearishCount: number;
    neutralCount: number;
  };
  performanceMetrics: {
    activeCoinCount: number;
    cacheHitRate: number;
    avgProcessingTime: number;
  };
}

export default function FuturesCoinList() {
  const router = useRouter();
  const [coins, setCoins] = useState<FuturesCoinListItem[]>([]);
  const [stats, setStats] = useState<FuturesCoinListStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [dataReady, setDataReady] = useState(false);
  const [filterSignal, setFilterSignal] = useState<'ALL' | 'BUY' | 'SELL' | 'HOLD'>('ALL');
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  const [isConnected, setIsConnected] = useState(false);
  const [serviceStatus, setServiceStatus] = useState<{lastUpdate: number, isActive: boolean} | null>(null);

  // Notification system state
  const [notificationCronActive, setNotificationCronActive] = useState(false);

  // Use ref to access current coins data
  const coinsRef = useRef<FuturesCoinListItem[]>([]);

  // Update ref whenever coins change
  useEffect(() => {
    coinsRef.current = coins;
  }, [coins]);

  const timeframes = [
    { key: '5m', label: '5m' },
    { key: '15m', label: '15m' },
    { key: '1h', label: '1h' },
    { key: '4h', label: '4h' },
    { key: '1d', label: '1d' }
  ] as const;

  // Futures notification cron job function
  const startFuturesNotificationCronJob = useCallback(async (): Promise<(() => void) | null> => {
    console.log('🔔 Starting futures notification cron job...');
    setNotificationCronActive(true);

    // Check futures notification rules every 30 seconds
    const cronInterval = setInterval(async () => {
      try {
        // Get current futures coins data from ref (always up-to-date)
        const currentCoins = coinsRef.current;

        // Only run if we have coins data (stops checking when user navigates away)
        if (currentCoins.length === 0) {
          console.log('🔔 Futures notification cron: No coins loaded - skipping rule check (user may have navigated away)');
          return;
        }

        console.log('🔔 Futures notification cron: Checking rules against', currentCoins.length, 'futures coins');

        // Call the backend endpoint to check futures notification rules
        const response = await fetch(getApiUrl('/api/admin/futures-notification-rules/check'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            coins: currentCoins.map((coin: FuturesCoinListItem) => ({
              symbol: coin.symbol,
              name: coin.name,
              price: coin.price,
              priceChange24h: coin.priceChange24h,
              volume: coin.volume,
              confidence: coin.confidence,
              lastUpdated: coin.lastUpdated,
              marketType: 'FUTURES'
            }))
          })
        });

        if (response.ok) {
          const result = await response.json();
          console.log('🔔 Futures notification rules checked successfully:', result.message);
        } else {
          console.error('🔔 Failed to check futures notification rules:', response.status);
        }

        // Also check futures early warning alert rules
        const futuresEarlyWarningResponse = await fetch(getApiUrl('/api/early-warning-alert-rules/check'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            marketType: 'FUTURES',
            coins: currentCoins.map((coin: FuturesCoinListItem) => ({
              symbol: coin.symbol,
              name: coin.name,
              price: coin.price,
              priceChange24h: coin.priceChange24h,
              volume: coin.volume,
              confidence: coin.confidence,
              lastUpdated: coin.lastUpdated
            }))
          })
        });

        // Handle futures early warning alert rules response
        if (futuresEarlyWarningResponse.ok) {
          const futuresEarlyWarningResult = await futuresEarlyWarningResponse.json();
          if (futuresEarlyWarningResult.success && futuresEarlyWarningResult.data.length > 0) {
            console.log('🚨 Futures early warning alert rules triggered:', futuresEarlyWarningResult.data.length, 'alerts');

            // Show toast notifications for triggered futures early warning alerts
            futuresEarlyWarningResult.data.forEach((alert: any) => {
              showFuturesEarlyWarningToast(alert);
            });
          }
        } else {
          console.error('🚨 Futures early warning alert rule check failed', futuresEarlyWarningResponse.status);
        }
      } catch (error) {
        console.error('🔔 Error in futures notification cron job:', error);
      }
    }, 30000); // Every 30 seconds

    // Return cleanup function
    return () => {
      console.log('🔔 Stopping futures notification cron job');
      clearInterval(cronInterval);
      setNotificationCronActive(false);
    };
  }, []); // Remove the problematic dependency

  // Function to reposition all futures toasts after one is removed
  const repositionFuturesToasts = () => {
    const toasts = document.querySelectorAll('.futures-early-warning-toast');
    toasts.forEach((toast, index) => {
      const element = toast as HTMLElement;
      element.style.top = `${16 + (index * 140)}px`; // Increased spacing from 120px to 140px
    });
  };

  // Clear all existing futures toasts
  const clearAllFuturesToasts = () => {
    const toasts = document.querySelectorAll('.futures-early-warning-toast');
    toasts.forEach(toast => toast.remove());
  };

  // Make repositionFuturesToasts available globally for onclick handlers
  useEffect(() => {
    (window as any).repositionFuturesToasts = repositionFuturesToasts;
    (window as any).clearAllFuturesToasts = clearAllFuturesToasts;

    // Clear any existing futures toasts on component mount
    clearAllFuturesToasts();

    return () => {
      delete (window as any).repositionFuturesToasts;
      delete (window as any).clearAllFuturesToasts;
    };
  }, []);

  // Show futures early warning toast notification (matching spot design but with futures styling)
  const showFuturesEarlyWarningToast = (alert: any) => {
    // Get existing toasts to calculate position
    const existingToasts = document.querySelectorAll('.futures-early-warning-toast');
    const topOffset = 16 + (existingToasts.length * 140); // 16px base + 140px per toast

    // Create enhanced toast element for futures
    const toast = document.createElement('div');
    const toastId = `futures-early-warning-toast-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    // Priority-based gradient colors with futures theme (orange/yellow tones)
    const priorityGradient = alert.priority === 'HIGH'
      ? 'from-orange-500 to-red-600'
      : alert.priority === 'MEDIUM'
      ? 'from-yellow-500 to-orange-600'
      : 'from-green-500 to-yellow-600';

    toast.id = toastId;
    toast.className = `futures-early-warning-toast fixed right-4 w-full max-w-sm bg-gradient-to-r ${priorityGradient} shadow-2xl rounded-xl border border-white/20 backdrop-blur-sm transform transition-all duration-500 ease-out z-50 animate-slide-in-right`;
    toast.style.top = `${topOffset}px`;
    toast.style.animationDelay = `${existingToasts.length * 100}ms`;

    // Alert type styling
    const alertTypeIcon = alert.alertType === 'PUMP_LIKELY' ? '🚀' : alert.alertType === 'DUMP_LIKELY' ? '📉' : '⚠️';
    const alertTypeText = alert.alertType === 'PUMP_LIKELY' ? 'PUMP LIKELY' : alert.alertType === 'DUMP_LIKELY' ? 'DUMP LIKELY' : 'NEUTRAL';

    toast.innerHTML = `
      <div class="p-4 text-white">
        <div class="flex items-start justify-between mb-3">
          <div class="flex items-center space-x-2">
            <span class="text-xl">${alertTypeIcon}</span>
            <div>
              <div class="font-bold text-lg">Futures Early Warning</div>
              <div class="text-sm opacity-90">${alert.symbol.replace('USDT', '')}</div>
            </div>
          </div>
          <button onclick="this.parentElement.parentElement.remove(); repositionFuturesToasts();" class="text-white/80 hover:text-white transition-colors p-1 rounded-full hover:bg-white/10">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>

        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <span class="bg-white/20 px-2 py-1 rounded-full text-xs font-medium">${alertTypeText}</span>
            <span class="text-xs opacity-90">${alert.priority} Priority</span>
          </div>

          <div class="flex items-center justify-between text-xs">
            <span class="bg-white/15 px-2 py-1 rounded">Confidence: ${alert.confidence}%</span>
            <span class="bg-white/15 px-2 py-1 rounded">ETA: ${alert.timeEstimate}</span>
          </div>

          <div class="text-xs opacity-75 pt-1 border-t border-white/20">
            Rule: ${alert.ruleName} • Futures Market
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(toast);

    console.log('🚨 Futures Early Warning Toast displayed for', alert.symbol, alert.alertType, `(${alert.priority} priority)`);

    // Auto-remove after 30 seconds
    setTimeout(() => {
      if (toast.parentElement) {
        toast.remove();
        repositionFuturesToasts();
      }
    }, 30000);
  };

  // Fetch futures coin list data - works exactly like spot coin list
  const fetchFuturesCoinList = async (refresh = false) => {
    try {
      if (refresh) setRefreshing(true);
      else setLoading(true);

      // Fetch top 50 futures coins and stats (same as spot)
      const [coinsResponse, statsResponse] = await Promise.all([
        fetch(getApiUrl(`${API_CONFIG.ENDPOINTS.FUTURES_COIN_LIST}/top50`)),
        fetch(getApiUrl(API_CONFIG.ENDPOINTS.FUTURES_COIN_LIST_STATS))
      ]);

      if (coinsResponse.ok && statsResponse.ok) {
        const coinsData = await coinsResponse.json();
        const statsData = await statsResponse.json();

        console.log('🚀 Fetched futures coin list data:', coinsData.data?.length || 0, 'coins');
        console.log('🔍 All futures coins received:', coinsData.data?.map((c: any) => c.symbol) || []);

        setCoins(coinsData.data || []);
        setStats(statsData.data || null);
        setLastUpdate(new Date());
        setDataReady(true); // Always ready when we get data

        setLoading(false);
        setRefreshing(false);
      } else {
        console.error('Failed to fetch futures coin list data', {
          coinsStatus: coinsResponse.status,
          statsStatus: statsResponse.status
        });
        setLoading(false);
        setRefreshing(false);
      }
    } catch (error) {
      console.error('Error fetching futures coin list:', error);
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Check service status
  const checkServiceStatus = async () => {
    try {
      const response = await fetch(getApiUrl(API_CONFIG.ENDPOINTS.FUTURES_COIN_LIST_STATS));
      if (response.ok) {
        const data = await response.json();
        setServiceStatus({
          lastUpdate: data.data?.lastUpdate || Date.now(),
          isActive: true
        });
      }
    } catch (error) {
      console.error('Error checking futures service status:', error);
      setServiceStatus({
        lastUpdate: Date.now(),
        isActive: false
      });
    }
  };

  // Format price with appropriate decimal places
  const formatPrice = (price: number): string => {
    if (price >= 1000) return `$${price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    if (price >= 1) return `$${price.toFixed(4)}`;
    if (price >= 0.01) return `$${price.toFixed(6)}`;
    return `$${price.toFixed(8)}`;
  };

  // Format volume
  const formatVolume = (volume: number): string => {
    if (volume >= 1e9) return `$${(volume / 1e9).toFixed(2)}B`;
    if (volume >= 1e6) return `$${(volume / 1e6).toFixed(2)}M`;
    if (volume >= 1e3) return `$${(volume / 1e3).toFixed(2)}K`;
    return `$${volume.toFixed(2)}`;
  };

  // Get confidence bar color
  const getConfidenceColor = (action: string): string => {
    if (action === 'BUY') return 'bg-green-500';
    if (action === 'SELL') return 'bg-red-500';
    return 'bg-yellow-500';
  };

  // Get confidence background color
  const getConfidenceBackgroundColor = (action: string): string => {
    if (action === 'BUY') return 'bg-green-500/10';
    if (action === 'SELL') return 'bg-red-500/10';
    return 'bg-yellow-500/10';
  };

  // State for futures notifications
  const [futuresNotifications, setFuturesNotifications] = useState<any[]>([]);

  // Initialize WebSocket connection for real-time updates
  useEffect(() => {
    const newSocket = io(getWebSocketUrl(), {
      transports: ['websocket', 'polling']
    });

    newSocket.on('connect', () => {
      console.log('Connected to WebSocket for futures coin list');
      setIsConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from WebSocket');
      setIsConnected(false);
    });

    // Listen for futures coin list updates
    newSocket.on('futuresCoinListUpdate', (data: { data: FuturesCoinListItem[], timestamp: number }) => {
      console.log('📊 Received futures coin list update:', data.data.length, 'coins');
      setCoins(data.data);
      setLastUpdate(new Date(data.timestamp));
    });

    // Listen for individual futures coin price updates
    newSocket.on('futuresCoinPriceUpdate', (data: { symbol: string, price: number, priceChange24h: number, volume: number, timestamp: number }) => {
      console.log('🔄 Real-time futures price update:', data.symbol, '$' + data.price.toFixed(6), data.priceChange24h.toFixed(2) + '%');

      setCoins((prevCoins: FuturesCoinListItem[]) =>
        prevCoins.map((coin: FuturesCoinListItem) =>
          coin.symbol === data.symbol
            ? { ...coin, price: data.price, priceChange24h: data.priceChange24h, volume: data.volume, lastUpdated: data.timestamp }
            : coin
        )
      );
      setLastUpdate(new Date(data.timestamp));
    });

    // Listen for futures notifications and pass them to the notification system
    newSocket.on('notification', (payload: any) => {
      console.log('🔔 FuturesCoinList received notification:', payload.title);

      // Only handle futures notifications
      if (payload.marketType === 'FUTURES' || payload.title?.includes('FUTURES:')) {
        console.log('🔔 Processing futures notification in FuturesCoinList:', payload.title);
        setFuturesNotifications(prev => [payload, ...prev]);
      }
    });

    // Listen for futures early warning toast alerts
    newSocket.on('futuresEarlyWarningToast', (data: any) => {
      console.log('🚨 Futures early warning toast received:', data.symbol, data.alertType);
      showFuturesEarlyWarningToast(data);
    });

    return () => {
      console.log('🔌 FuturesCoinList component unmounting - closing WebSocket connection');
      newSocket.close();
    };
  }, []);

  // Initial load and periodic status checks
  useEffect(() => {
    console.log('🚀 FuturesCoinList component mounted - fetching fresh top 50 futures coins...');
    fetchFuturesCoinList();
    checkServiceStatus();

    // Start futures notification cron job
    let notificationCleanup: (() => void) | null = null;
    startFuturesNotificationCronJob().then(cleanup => {
      notificationCleanup = cleanup;
    });

    // Check service status every 30 seconds
    const statusInterval = setInterval(() => {
      checkServiceStatus();
    }, 30000);

    return () => {
      console.log('🔄 FuturesCoinList component unmounting - clearing status check interval and notification cron');
      clearInterval(statusInterval);
      if (notificationCleanup) {
        notificationCleanup();
      }
    };
  }, []); // Remove the problematic dependency - only run once on mount

  // Calculate smart score for sorting (combines confidence and strength across timeframes)
  const calculateSmartScore = (coin: FuturesCoinListItem, targetAction?: 'BUY' | 'SELL' | 'HOLD') => {
    const timeframes = ['5m', '15m', '1h', '4h', '1d'] as const;
    let totalScore = 0;
    let matchingSignals = 0;

    timeframes.forEach(tf => {
      const signal = coin.confidence[tf];

      // If filtering by specific action, only count matching signals
      if (targetAction && signal.action !== targetAction) {
        return;
      }

      // Weight formula: (confidence + strength) / 2, with bonus for matching action
      let score = (signal.confidence + signal.strength) / 2;

      // Bonus for strong signals (BUY/SELL vs HOLD)
      if (signal.action !== 'HOLD') {
        score *= 1.2;
      }

      // Additional bonus for high confidence + high strength combinations
      if (signal.confidence >= 70 && signal.strength >= 70) {
        score *= 1.3;
      }

      totalScore += score;
      matchingSignals++;
    });

    // Return average score, or 0 if no matching signals
    return matchingSignals > 0 ? totalScore / matchingSignals : 0;
  };

  // Filter and sort coins based on search and signal filter
  const filteredCoins = coins
    .filter((coin: FuturesCoinListItem) => {
      const matchesSearch = coin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           coin.symbol.toLowerCase().includes(searchTerm.toLowerCase());

      if (!matchesSearch) return false;

      if (filterSignal === 'ALL') return true;

      // Check if any timeframe has the target signal
      const timeframes = ['5m', '15m', '1h', '4h', '1d'] as const;
      return timeframes.some(tf => coin.confidence[tf]?.action === filterSignal);
    })
    .sort((a: FuturesCoinListItem, b: FuturesCoinListItem) => {
      // Smart sorting based on confidence and strength
      const targetAction = filterSignal === 'ALL' ? undefined : filterSignal;
      const scoreA = calculateSmartScore(a, targetAction);
      const scoreB = calculateSmartScore(b, targetAction);

      // Sort by score descending (highest scores first)
      return scoreB - scoreA;
    });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center max-w-md mx-auto">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-6"></div>
          <p className="text-xl font-semibold mb-4">Loading futures coin list...</p>
          <p className="text-gray-400">Fetching real-time futures market data</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <button
              onClick={() => router.push('/')}
              className="p-2 text-gray-400 hover:text-white rounded-lg hover:bg-gray-800 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                Futures Market
              </h1>
              <p className="text-gray-400 mt-1">
                Binance USDⓈ-M Futures • Top 50 Contracts • Real-time Analysis
              </p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {/* Market Type Toggle */}
            <div className="flex bg-gray-800 rounded-lg p-1">
              <Link href="/">
                <button className="px-4 py-2 text-sm rounded-md text-gray-400 hover:text-white transition-colors">
                  Spot
                </button>
              </Link>
              <button className="px-4 py-2 text-sm rounded-md bg-blue-600 text-white">
                Futures
              </button>
            </div>

            {/* Futures Notification System */}
            <FuturesNotificationSystem externalNotifications={futuresNotifications} />

            {/* Futures Early Warning System */}
            <FuturesEarlyWarningSystem />

            <button
              onClick={() => fetchFuturesCoinList(true)}
              disabled={refreshing}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded-lg transition-colors"
            >
              <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-gradient-to-br from-gray-800/90 to-black/90 backdrop-blur-md rounded-xl border border-gray-600/30 p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-500/20 rounded-lg">
                  <BarChart3 className="w-5 h-5 text-blue-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">Total Futures</p>
                  <p className="text-xl font-bold">{stats.totalCoins}</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-gray-800/90 to-black/90 backdrop-blur-md rounded-xl border border-gray-600/30 p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-500/20 rounded-lg">
                  <DollarSign className="w-5 h-5 text-green-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">24h Volume</p>
                  <p className="text-xl font-bold">{formatVolume(stats.marketSummary.totalVolume)}</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-gray-800/90 to-black/90 backdrop-blur-md rounded-xl border border-gray-600/30 p-4">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-lg ${stats.marketSummary.avgPriceChange >= 0 ? 'bg-green-500/20' : 'bg-red-500/20'}`}>
                  {stats.marketSummary.avgPriceChange >= 0 ?
                    <TrendingUp className="w-5 h-5 text-green-400" /> :
                    <TrendingDown className="w-5 h-5 text-red-400" />
                  }
                </div>
                <div>
                  <p className="text-sm text-gray-400">Avg Change</p>
                  <p className={`text-xl font-bold ${stats.marketSummary.avgPriceChange >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {stats.marketSummary.avgPriceChange >= 0 ? '+' : ''}{stats.marketSummary.avgPriceChange.toFixed(2)}%
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-gray-800/90 to-black/90 backdrop-blur-md rounded-xl border border-gray-600/30 p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-500/20 rounded-lg">
                  <Activity className="w-5 h-5 text-purple-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">Market Sentiment</p>
                  <div className="flex gap-1 mt-1">
                    <span className="text-xs text-green-400">↑{stats.marketSummary.bullishCount}</span>
                    <span className="text-xs text-red-400">↓{stats.marketSummary.bearishCount}</span>
                    <span className="text-xs text-yellow-400">→{stats.marketSummary.neutralCount}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Status Indicators */}
        <div className="flex items-center gap-4 mb-4 text-sm">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-gray-400">
              WebSocket: {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>

          {serviceStatus && (
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${serviceStatus.isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-gray-400">
                Futures Service: {serviceStatus.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          )}

          {lastUpdate && (
            <div className="flex items-center gap-2">
              <Zap className="w-3 h-3 text-blue-400" />
              <span className="text-gray-400">
                Last Update: {lastUpdate.toLocaleTimeString()}
              </span>
            </div>
          )}
        </div>

        {/* Controls */}
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center mb-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search futures contracts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-800/50 border border-gray-600/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            />
          </div>

          {/* Signal Filter */}
          <div className="flex gap-2">
            {(['ALL', 'BUY', 'SELL', 'HOLD'] as const).map((signal) => (
              <button
                key={signal}
                onClick={() => setFilterSignal(signal)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  filterSignal === signal
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-800/50 text-gray-400 hover:text-white hover:bg-gray-700/50'
                }`}
              >
                {signal}
              </button>
            ))}
          </div>
        </div>

        {/* Coin List Table */}
        <div className="bg-gradient-to-br from-gray-800/90 to-black/90 backdrop-blur-md rounded-xl border border-gray-600/30 shadow-xl overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-900/50">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-medium text-gray-300">Contract</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">Price</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">24h Change</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-300">5m</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-300">15m</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-300">1h</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-300">4h</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-300">1d</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-300">View</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700/50">
                {filteredCoins.map((coin: FuturesCoinListItem) => (
                  <tr
                    key={coin.symbol}
                    className="hover:bg-gray-800/30 transition-colors"
                  >
                    <td className="px-6 py-4">
                      <div>
                        <div className="font-medium text-white">{coin.name}</div>
                        <div className="text-sm text-gray-400">{coin.symbol}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-right">
                      <div className="font-medium text-white">
                        {formatPrice(coin.price)}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-right">
                      <div className={`font-medium ${coin.priceChange24h >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {coin.priceChange24h >= 0 ? '+' : ''}{coin.priceChange24h.toFixed(2)}%
                      </div>
                    </td>
                    {timeframes.map(({ key }) => {
                      const tfData = coin.confidence[key];
                      if (!tfData) {
                        return (
                          <td key={key} className="px-6 py-4 text-center">
                            <div className="text-gray-500 text-sm">-</div>
                          </td>
                        );
                      }

                      return (
                        <td key={key} className="px-6 py-4">
                          <div className={`rounded-lg p-2 ${getConfidenceBackgroundColor(tfData.action)}`}>
                            <div className="text-xs font-medium text-center mb-1">
                              {tfData.action}
                            </div>
                            <div className="flex gap-1 mb-1">
                              <div className="flex-1 bg-gray-700 rounded-full h-1">
                                <div
                                  className={`h-1 rounded-full ${getConfidenceColor(tfData.action)}`}
                                  style={{ width: `${tfData.confidence}%` }}
                                ></div>
                              </div>
                              <div className="flex-1 bg-gray-700 rounded-full h-1">
                                <div
                                  className={`h-1 rounded-full ${getConfidenceColor(tfData.action)}`}
                                  style={{ width: `${tfData.strength}%` }}
                                ></div>
                              </div>
                            </div>
                            <div className="text-xs text-center text-gray-400">
                              {tfData.confidence.toFixed(0)}% • {tfData.strength.toFixed(0)}%
                            </div>
                          </div>
                        </td>
                      );
                    })}
                    <td className="px-6 py-4 text-center">
                      <Link href={`/futures/analysis/${coin.symbol}`}>
                        <button className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors">
                          Analyze
                        </button>
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredCoins.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-400 text-lg">No futures contracts found</p>
              <p className="text-gray-500 text-sm mt-2">
                {searchTerm ? 'Try adjusting your search terms' : 'Loading futures data...'}
              </p>
            </div>
          )}
        </div>

        {/* Footer Info */}
        <div className="mt-6 text-center text-sm text-gray-500">
          <p>
            Showing {filteredCoins.length} of {coins.length} futures contracts •
            Data from Binance USDⓈ-M Futures WebSocket •
            Updated every 10 seconds
          </p>
        </div>
      </div>

    </div>
  );
}
