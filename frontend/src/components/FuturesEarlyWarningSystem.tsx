'use client';

import { useState, useEffect } from 'react';
import { Zap, X, Refresh<PERSON><PERSON>, Eye } from 'lucide-react';
import { io } from 'socket.io-client';
import { getApiUrl, getWebSocketUrl } from '../utils/api';
import { playEarlyWarningSound } from '../utils/soundManager';

interface FuturesEarlyWarningAlert {
  id: string;
  symbol: string;
  alertType: 'PUMP_LIKELY' | 'DUMP_LIKELY' | 'NEUTRAL';
  confidence: number;
  timeEstimateMin: number;
  timeEstimateMax: number;
  triggeredBy: string[];
  currentPrice: number;
  volume24h?: number;
  priceChange24h?: number;
  createdAt: string;
  triggeredAt?: string;
  isRead?: boolean;
  readAt?: string;
  phase1Score: number;
  phase2Score: number;
  phase3Score: number;
  marketType: string;
}

export default function FuturesEarlyWarningSystem() {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'recent' | 'all'>('recent');
  const [recentAlerts, setRecentAlerts] = useState<FuturesEarlyWarningAlert[]>([]);
  const [allAlerts, setAllAlerts] = useState<FuturesEarlyWarningAlert[]>([]);
  const [recentCount, setRecentCount] = useState(0);
  const [loading, setLoading] = useState(false);

  // Fetch futures early warning alerts
  const fetchAlerts = async (type: 'recent' | 'all') => {
    try {
      setLoading(true);
      const response = await fetch(getApiUrl(`/api/early-warning-alerts?type=${type}&marketType=FUTURES`));
      const data = await response.json();
      
      if (data.success) {
        if (type === 'recent') {
          setRecentAlerts(data.data);
          setRecentCount(data.data.filter((alert: FuturesEarlyWarningAlert) => !alert.isRead).length);
        } else {
          setAllAlerts(data.data);
        }
      }
    } catch (error) {
      console.error('Error fetching futures early warning alerts:', error);
    } finally {
      setLoading(false);
    }
  };

  // Mark alert as read
  const markAsRead = async (alertId: string) => {
    try {
      await fetch(getApiUrl(`/api/early-warning-alerts/${alertId}/read`), {
        method: 'POST'
      });
      
      // Update local state
      setRecentAlerts(prev => 
        prev.map(alert => 
          alert.id === alertId ? { ...alert, isRead: true, readAt: new Date().toISOString() } : alert
        )
      );
      setAllAlerts(prev => 
        prev.map(alert => 
          alert.id === alertId ? { ...alert, isRead: true, readAt: new Date().toISOString() } : alert
        )
      );
      
      // Update count
      setRecentCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Error marking futures alert as read:', error);
    }
  };

  // Initialize WebSocket connection for real-time futures alerts
  useEffect(() => {
    const socketConnection = io(getWebSocketUrl(), {
      transports: ['websocket', 'polling']
    });

    socketConnection.on('connect', () => {
      console.log('🚨 Futures Early Warning System connected to WebSocket');
    });

    socketConnection.on('disconnect', () => {
      console.log('🚨 Futures Early Warning System disconnected from WebSocket');
    });

    // Listen for new futures early warning alerts
    socketConnection.on('futuresEarlyWarning', (alert: any) => {
      console.log('🚨 New futures early warning alert received:', alert.symbol, alert.alertType);

      // Play early warning sound
      playEarlyWarningSound().catch(e =>
        console.log('Could not play early warning sound:', e)
      );

      // Update recent count
      setRecentCount(prev => prev + 1);
    });

    return () => {
      socketConnection.close();
    };
  }, []);

  // Load alerts when tab changes
  useEffect(() => {
    if (isOpen) {
      fetchAlerts(activeTab);
    }
  }, [activeTab, isOpen]);

  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds}s ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  return (
    <>
      {/* Futures Early Warning System Button */}
      <div className="relative">
        <button
          onClick={() => {
            setIsOpen(!isOpen);
            if (!isOpen) {
              fetchAlerts(activeTab);
            }
          }}
          className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-yellow-500 rounded-md"
          title="Futures Early Warning System"
        >
          <Zap className="h-6 w-6 text-yellow-500" />
          {recentCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-yellow-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {recentCount > 99 ? '99+' : recentCount}
            </span>
          )}
        </button>

        {/* Futures Early Warning Panel */}
        {isOpen && (
          <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-h-96 overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-yellow-500 to-orange-500">
              <div className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-white" />
                <h3 className="text-lg font-semibold text-white">Futures Early Warning</h3>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Tabs */}
            <div className="flex border-b border-gray-200">
              <button
                onClick={() => setActiveTab('recent')}
                className={`flex-1 px-4 py-2 text-sm font-medium ${
                  activeTab === 'recent'
                    ? 'text-yellow-600 border-b-2 border-yellow-500 bg-yellow-50'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Recent {recentCount > 0 && `(${recentCount})`}
              </button>
              <button
                onClick={() => setActiveTab('all')}
                className={`flex-1 px-4 py-2 text-sm font-medium ${
                  activeTab === 'all'
                    ? 'text-yellow-600 border-b-2 border-yellow-500 bg-yellow-50'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                All
              </button>
            </div>

            {/* Content */}
            <div className="max-h-80 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center p-8">
                  <RefreshCw className="h-6 w-6 animate-spin text-yellow-500" />
                  <span className="ml-2 text-gray-600">Loading futures alerts...</span>
                </div>
              ) : (
                <div className="p-4 space-y-3">
                  {(activeTab === 'recent' ? recentAlerts : allAlerts).length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Zap className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                      <p>No futures early warning alerts yet</p>
                    </div>
                  ) : (
                    (activeTab === 'recent' ? recentAlerts : allAlerts).map((alert) => {
                      const alertTypeIcon = alert.alertType === 'PUMP_LIKELY' ? '🚀' : alert.alertType === 'DUMP_LIKELY' ? '📉' : '⚠️';
                      const alertTypeText = alert.alertType === 'PUMP_LIKELY' ? 'PUMP LIKELY' : alert.alertType === 'DUMP_LIKELY' ? 'DUMP LIKELY' : 'NEUTRAL';
                      const alertTypeColor = alert.alertType === 'PUMP_LIKELY' ? 'text-green-600' : alert.alertType === 'DUMP_LIKELY' ? 'text-red-600' : 'text-yellow-600';

                      return (
                        <div
                          key={alert.id}
                          className={`p-3 rounded-lg border transition-colors cursor-pointer ${
                            alert.isRead
                              ? 'bg-gray-50 border-gray-200'
                              : 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100'
                          }`}
                          onClick={() => !alert.isRead && markAsRead(alert.id)}
                        >
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <span className="text-lg">{alertTypeIcon}</span>
                              <div>
                                <div className="font-semibold text-gray-900">
                                  {alert.symbol.replace('USDT', '')} • Futures
                                </div>
                                <div className={`text-sm font-medium ${alertTypeColor}`}>
                                  {alertTypeText}
                                </div>
                              </div>
                            </div>
                            <div className="text-xs text-gray-500">
                              {formatTimeAgo(alert.createdAt)}
                            </div>
                          </div>

                          <div className="space-y-1 text-xs text-gray-600">
                            <div className="flex justify-between">
                              <span>Confidence:</span>
                              <span className="font-medium">{alert.confidence}%</span>
                            </div>
                            <div className="flex justify-between">
                              <span>ETA:</span>
                              <span className="font-medium">{alert.timeEstimateMin}-{alert.timeEstimateMax} min</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Price:</span>
                              <span className="font-medium">${alert.currentPrice.toFixed(6)}</span>
                            </div>
                          </div>

                          {!alert.isRead && (
                            <div className="mt-2 flex justify-end">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  markAsRead(alert.id);
                                }}
                                className="text-xs text-yellow-600 hover:text-yellow-800 flex items-center space-x-1"
                              >
                                <Eye className="h-3 w-3" />
                                <span>Mark as read</span>
                              </button>
                            </div>
                          )}
                        </div>
                      );
                    })
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
}
