'use client';

import { useState } from 'react';
import { Bell, Settings, Zap, Volume2, TrendingUp } from 'lucide-react';
import NotificationRules from './NotificationRules';
import FuturesNotificationRules from './FuturesNotificationRules';
import EarlyWarningAlertRulesSettings from './EarlyWarningAlertRulesSettings';
import SoundTestPanel from './SoundTestPanel';

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState<'notifications' | 'futures-notifications' | 'spot-early-warning' | 'futures-early-warning' | 'sounds'>('notifications');

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <Settings className="h-7 w-7 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
            </div>
            <div className="text-sm text-gray-600">
              System Configuration
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('notifications')}
              className={`${
                activeTab === 'notifications'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <Bell className="h-4 w-4" />
              <span>Spot Notifications</span>
            </button>
            <button
              onClick={() => setActiveTab('futures-notifications')}
              className={`${
                activeTab === 'futures-notifications'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <TrendingUp className="h-4 w-4" />
              <span>Futures Notifications</span>
            </button>
            <button
              onClick={() => setActiveTab('spot-early-warning')}
              className={`${
                activeTab === 'spot-early-warning'
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <Zap className="h-4 w-4" />
              <span>Spot Early Warning</span>
            </button>
            <button
              onClick={() => setActiveTab('futures-early-warning')}
              className={`${
                activeTab === 'futures-early-warning'
                  ? 'border-yellow-500 text-yellow-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <Zap className="h-4 w-4" />
              <span>Futures Early Warning</span>
            </button>
            <button
              onClick={() => setActiveTab('sounds')}
              className={`${
                activeTab === 'sounds'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <Volume2 className="h-4 w-4" />
              <span>Sound Settings</span>
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'notifications' && <NotificationRules />}
        {activeTab === 'futures-notifications' && <FuturesNotificationRules />}
        {activeTab === 'spot-early-warning' && <EarlyWarningAlertRulesSettings marketType="SPOT" />}
        {activeTab === 'futures-early-warning' && <EarlyWarningAlertRulesSettings marketType="FUTURES" />}
        {activeTab === 'sounds' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Sound Configuration</h2>
              <p className="text-gray-600 mb-6">
                Test and configure notification sounds for different alert types.
              </p>
              <SoundTestPanel />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
