import { Server as SocketIOServer } from 'socket.io';
import { BinanceFuturesService, BinanceFuturesTicker } from './binanceFuturesService';
import { FuturesTechnicalAnalysis } from './futuresTechnicalAnalysis';
import { FuturesWhaleDetectionService } from './futuresWhaleDetectionService';
import { logDebug, logError, logInfo } from '../utils/logger';
import { SUPPORTED_TIMEFRAMES } from '../config/coinConfig';

// Interfaces matching spot early warning service exactly
interface FuturesVolumeAnalysis {
  currentVolume: number;
  avgVolume: number;
  ratio: number;
  priceChange: number;
  acceleration: number;
}

interface FuturesBuySellVolumeAnalysis {
  timeframe: string;
  currentBuyVolume: number;
  currentSellVolume: number;
  avgBuyVolume: number;
  avgSellVolume: number;
  buyVolumeRatio: number;
  sellVolumeRatio: number;
  buyVolumeSpike: boolean;
  sellVolumeSpike: boolean;
  dominantDirection: 'BUY' | 'SELL' | 'NEUTRAL';
  signal: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
}

interface FuturesMultiTimeframeVolumeAnalysis {
  symbol: string;
  timeframes: FuturesBuySellVolumeAnalysis[];
  overallSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  strongestTimeframe: string | null;
  score: number;
  detected: boolean;
}

interface FuturesRSIAnalysis {
  currentRSI: number;
  previousRSI: number;
  velocity: number;
  momentum: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
}

interface FuturesEMAAnalysis {
  ema20: number;
  ema50: number;
  gap: number;
  gapPercent: number;
  momentum: number;
  convergence: boolean;
}

export interface FuturesEarlyWarningAlert {
  id?: string;
  symbol: string;
  alertType: 'PUMP_LIKELY' | 'DUMP_LIKELY' | 'NEUTRAL';
  confidence: number;
  timeEstimateMin: number;
  timeEstimateMax: number;
  triggeredBy: string[];
  currentPrice: number;
  volume24h?: number;
  priceChange24h?: number;

  // Phase data (identical structure to spot)
  volumeSpike?: any;
  multiTimeframeVolumeAnalysis?: any;
  rsiMomentum?: any;
  emaConvergence?: any;
  bidAskImbalance?: any;
  priceAction?: any;
  whaleActivity?: any;

  // Scores (identical to spot)
  phase1Score: number;
  phase2Score: number;
  phase3Score: number;
}

export class FuturesEarlyWarningService {
  private futuresService: BinanceFuturesService;
  private technicalAnalysis: FuturesTechnicalAnalysis;
  private whaleDetectionService: FuturesWhaleDetectionService;
  private io?: SocketIOServer;

  // Data tracking maps (identical to spot)
  private volumeHistory: Map<string, number[]> = new Map();
  private rsiHistory: Map<string, number[]> = new Map();
  private priceHistory: Map<string, number[]> = new Map();
  private lastAlerts: Map<string, number> = new Map();

  // Order book tracking for futures (identical structure to spot)
  private orderBookCache: Map<string, any> = new Map();
  private wallTracker: Map<string, any[]> = new Map();

  // Volume tracking for multi-timeframe analysis (identical to spot)
  private volumeTracker: Map<string, Map<string, number[]>> = new Map();

  // System readiness tracking
  private isSystemReady: boolean = false;
  private systemStartTime: number = Date.now();
  private readonly MINIMUM_WARMUP_TIME_MS = 1 * 60 * 1000; // 1 minute minimum warmup
  private readonly MINIMUM_DATA_THRESHOLD = 10; // Minimum candles needed (reduced for faster startup)
  constructor(
    futuresService: BinanceFuturesService,
    technicalAnalysis: FuturesTechnicalAnalysis,
    io?: SocketIOServer
  ) {
    this.futuresService = futuresService;
    this.technicalAnalysis = technicalAnalysis;
    this.whaleDetectionService = new FuturesWhaleDetectionService(futuresService);
    this.io = io;

    logInfo('🚨 Futures Early Warning Service initialized - warming up data streams...');

    // Initialize tracking maps for all symbols
    this.initializeTrackingMaps();

    // Start system readiness monitoring
    this.startReadinessMonitoring();
  }

  // Initialize tracking maps for all symbols (identical to spot)
  private initializeTrackingMaps() {
    // Initialize volume tracking for all timeframes
    SUPPORTED_TIMEFRAMES.forEach(timeframe => {
      if (!this.volumeTracker.has(timeframe)) {
        this.volumeTracker.set(timeframe, new Map());
      }
    });
  }

  // Start monitoring system readiness
  private startReadinessMonitoring() {
    // Check readiness every 30 seconds
    const checkInterval = setInterval(() => {
      this.checkSystemReadiness();
    }, 30 * 1000);

    // Stop checking after 10 minutes (system should be ready by then)
    setTimeout(() => {
      clearInterval(checkInterval);
      if (!this.isSystemReady) {
        logInfo('🚨 Futures Early Warning System: Forcing readiness after 10 minutes');
        this.isSystemReady = true;
      }
    }, 10 * 60 * 1000);
  }

  // Check if system has sufficient data to start analysis
  private async checkSystemReadiness() {
    try {
      const now = Date.now();
      const timeSinceStart = now - this.systemStartTime;

      // Must wait at least minimum warmup time (reduced to 1 minute)
      if (timeSinceStart < this.MINIMUM_WARMUP_TIME_MS) {
        logInfo(`🚨 Futures Early Warning: Still warming up (${Math.round(timeSinceStart / 1000)}s / ${this.MINIMUM_WARMUP_TIME_MS / 1000}s)`);
        return;
      }

      // For testing: Force system ready after warmup period
      if (!this.isSystemReady) {
        this.isSystemReady = true;
        logInfo(`🚨 Futures Early Warning System: Ready! (Warmup period completed - ${Math.round(timeSinceStart / 1000)}s)`);
        return;
      }

      // Optional: Check if we have sufficient data for a sample symbol
      const testSymbol = 'BTCUSDT'; // Use BTC as test symbol
      try {
        const ohlcvData = await this.futuresService.getOHLCV(testSymbol, '15m', this.MINIMUM_DATA_THRESHOLD);
        if (ohlcvData && ohlcvData.length >= this.MINIMUM_DATA_THRESHOLD) {
          logInfo(`🚨 Futures Early Warning System: Data confirmed! (${ohlcvData.length} candles available for ${testSymbol})`);
        } else {
          logInfo(`🚨 Futures Early Warning: Limited data (${ohlcvData?.length || 0}/${this.MINIMUM_DATA_THRESHOLD} candles for ${testSymbol}) - but system ready`);
        }
      } catch (error) {
        logInfo(`🚨 Futures Early Warning: Data check failed for ${testSymbol} - but system ready for testing`);
      }
    } catch (error) {
      logError('Error checking futures early warning system readiness', error as Error);
    }
  }

  // Main analysis method (identical structure to spot)
  async analyzeSymbol(symbol: string): Promise<FuturesEarlyWarningAlert | null> {
    try {
      // Check if system is ready for analysis
      if (!this.isSystemReady) {
        logDebug(`🚨 Futures Early Warning: System not ready yet, skipping analysis for ${symbol}`);
        return null;
      }

      logInfo(`🚨 Futures Early Warning: Analyzing ${symbol}`);

      // Get current futures ticker data
      const ticker = this.futuresService.getCachedPrice(symbol);
      if (!ticker) {
        logDebug(`No futures ticker data available for ${symbol}`);
        return null;
      }

      const currentPrice = parseFloat(ticker.price);
      const volume24h = parseFloat(ticker.volume);
      const priceChange24h = parseFloat(ticker.priceChangePercent);

      // Phase 1: Volume & Momentum Detection (identical to spot)
      const phase1Result = await this.analyzePhase1(symbol, currentPrice, volume24h, priceChange24h);

      // Phase 2: Order Flow Analysis (identical to spot)
      const phase2Result = await this.analyzePhase2(symbol);

      // Phase 3: Whale Activity (identical to spot)
      const phase3Result = await this.analyzePhase3(symbol);

      // Log phase results for debugging
      logInfo(`🚨 Futures Phase Results for ${symbol}:`, {
        phase1Score: phase1Result.score,
        phase1Signal: phase1Result.overallSignal,
        phase2Score: phase2Result.score,
        phase2Signal: phase2Result.overallSignal,
        phase3Score: phase3Result.score,
        phase3Signal: phase3Result.overallSignal
      });

      // Calculate overall confidence and alert type (identical to spot)
      const alert = this.calculateOverallAlert(
        symbol, currentPrice, volume24h, priceChange24h,
        phase1Result, phase2Result, phase3Result
      );

      // Log alert calculation result
      if (alert) {
        logInfo(`🚨 Futures Alert Calculated for ${symbol}:`, {
          alertType: alert.alertType,
          confidence: alert.confidence,
          triggeredBy: alert.triggeredBy,
          phase1Score: alert.phase1Score,
          phase2Score: alert.phase2Score,
          phase3Score: alert.phase3Score
        });
      } else {
        logInfo(`🚨 No alert generated for ${symbol} - insufficient triggers or confidence`);
      }

      // Only process PUMP_LIKELY and DUMP_LIKELY signals, skip NEUTRAL
      if (alert && alert.alertType !== 'NEUTRAL' && alert.confidence > 0 && this.shouldCreateAlert(symbol, alert.alertType)) {
        logInfo(`✅ Returning valid futures alert for ${symbol}:`, {
          alertType: alert.alertType,
          confidence: alert.confidence,
          triggeredBy: alert.triggeredBy
        });
        return alert;
      }

      // Log why alert was rejected
      if (alert) {
        logInfo(`❌ Futures alert rejected for ${symbol}:`, {
          alertType: alert.alertType,
          confidence: alert.confidence,
          reason: alert.confidence <= 0 ? 'Low confidence' : 'Cooldown active'
        });
      }

      return null;
    } catch (error) {
      logError(`Error analyzing futures symbol ${symbol}`, error as Error);
      return null;
    }
  }

  // Check if we should create an alert (cooldown logic - reduced for testing)
  private shouldCreateAlert(symbol: string, alertType: string): boolean {
    const now = Date.now();
    const lastAlertKey = `${symbol}_${alertType}`;
    const lastAlert = this.lastAlerts.get(lastAlertKey);

    if (lastAlert && (now - lastAlert) < 30 * 1000) { // 30 second cooldown for testing
      return false;
    }

    this.lastAlerts.set(lastAlertKey, now);
    return true;
  }

  // Phase 1: Volume & Momentum Detection (Max 25 points total - identical to spot)
  private async analyzePhase1(symbol: string, currentPrice: number, volume24h: number, priceChange24h: number) {
    const results = {
      volumeSpike: null as any,
      multiTimeframeVolumeAnalysis: null as any,
      rsiMomentum: null as any,
      emaConvergence: null as any,
      score: 0,
      subComponentScores: {
        multiTimeframeVolume: 0,
        rsiMomentum: 0,
        emaConvergence: 0
      },
      overallSignal: 'NEUTRAL' as 'BULLISH' | 'BEARISH' | 'NEUTRAL'
    };

    let volumeScore = 0;
    let rsiScore = 0;
    let emaScore = 0;
    let volumeSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
    let rsiSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
    let emaSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';

    try {
      // Sub-component 1: Multi-timeframe Volume Analysis (8.33 points max)
      const multiTimeframeVolumeAnalysis = await this.performMultiTimeframeVolumeAnalysis(symbol);
      if (multiTimeframeVolumeAnalysis.detected) {
        results.multiTimeframeVolumeAnalysis = multiTimeframeVolumeAnalysis;

        // Calculate score based on signal strength and timeframe consensus
        const baseScore = multiTimeframeVolumeAnalysis.score * 8.33; // Max 8.33 points
        volumeScore = Math.min(8.33, baseScore);
        volumeSignal = multiTimeframeVolumeAnalysis.overallSignal;

        logDebug(`Futures multi-timeframe volume analysis for ${symbol}:`, {
          detected: true,
          overallSignal: multiTimeframeVolumeAnalysis.overallSignal,
          strongestTimeframe: multiTimeframeVolumeAnalysis.strongestTimeframe,
          score: volumeScore
        });
      }

      // Sub-component 2: RSI Momentum Analysis (8.33 points max)
      const rsiMomentumAnalysis = await this.analyzeRSIMomentum(symbol);
      if (rsiMomentumAnalysis && rsiMomentumAnalysis.detected) {
        results.rsiMomentum = rsiMomentumAnalysis;

        // Calculate score based on RSI momentum strength
        const baseScore = Math.abs(rsiMomentumAnalysis.velocity) * 8.33; // Max 8.33 points
        rsiScore = Math.min(8.33, baseScore);
        rsiSignal = rsiMomentumAnalysis.momentum;

        logDebug(`Futures RSI momentum analysis for ${symbol}:`, {
          detected: true,
          momentum: rsiMomentumAnalysis.momentum,
          velocity: rsiMomentumAnalysis.velocity,
          score: rsiScore
        });
      }

      // Sub-component 3: EMA Convergence Analysis (8.33 points max)
      const emaConvergenceAnalysis = await this.analyzeEMAConvergence(symbol);
      if (emaConvergenceAnalysis && emaConvergenceAnalysis.convergence) {
        results.emaConvergence = {
          detected: true,
          ...emaConvergenceAnalysis
        };

        // Calculate score based on EMA momentum and convergence strength
        const baseScore = Math.abs(emaConvergenceAnalysis.momentum) * 8.33; // Max 8.33 points
        emaScore = Math.min(8.33, baseScore);
        emaSignal = emaConvergenceAnalysis.momentum > 0 ? 'BULLISH' : 'BEARISH';

        logDebug(`Futures EMA convergence analysis for ${symbol}:`, {
          detected: true,
          convergence: true,
          momentum: emaConvergenceAnalysis.momentum,
          score: emaScore
        });
      }

      // Store sub-component scores
      results.subComponentScores.multiTimeframeVolume = volumeScore;
      results.subComponentScores.rsiMomentum = rsiScore;
      results.subComponentScores.emaConvergence = emaScore;

      // Calculate directional score using the same logic as spot
      let directionalScore = 0;

      // Apply directional scoring for volume
      if (volumeSignal === 'BULLISH') {
        directionalScore += volumeScore;
      } else if (volumeSignal === 'BEARISH') {
        directionalScore -= volumeScore;
      }

      // Apply directional scoring for RSI
      if (rsiSignal === 'BULLISH') {
        directionalScore += rsiScore;
      } else if (rsiSignal === 'BEARISH') {
        directionalScore -= rsiScore;
      }

      // Apply directional scoring for EMA
      if (emaSignal === 'BULLISH') {
        directionalScore += emaScore;
      } else if (emaSignal === 'BEARISH') {
        directionalScore -= emaScore;
      }

      // Determine overall Phase 1 signal based on net directional score
      if (directionalScore > 0) {
        results.overallSignal = 'BULLISH';
      } else if (directionalScore < 0) {
        results.overallSignal = 'BEARISH';
      } else {
        results.overallSignal = 'NEUTRAL';
      }

      // Store absolute score for legacy compatibility
      results.score = Math.abs(directionalScore);

    } catch (error) {
      logError(`Error in futures Phase 1 analysis for ${symbol}`, error as Error);
    }

    logInfo(`Futures Phase 1 Analysis for ${symbol}:`, {
      volumeSpike: results.volumeSpike?.detected || false,
      multiTimeframeVolumeAnalysis: results.multiTimeframeVolumeAnalysis?.detected || false,
      timeframesAnalyzed: results.multiTimeframeVolumeAnalysis?.timeframes?.length || 0,
      overallVolumeSignal: results.multiTimeframeVolumeAnalysis?.overallSignal || 'NEUTRAL',
      strongestTimeframe: results.multiTimeframeVolumeAnalysis?.strongestTimeframe || null,
      rsiMomentum: results.rsiMomentum?.detected || false,
      emaConvergence: results.emaConvergence?.detected || false,
      score: results.score
    });

    return results;

  }

  // Multi-timeframe volume analysis (identical to spot)
  private async performMultiTimeframeVolumeAnalysis(symbol: string): Promise<FuturesMultiTimeframeVolumeAnalysis> {
    const timeframes = ['5m', '15m', '1h', '4h']; // Same timeframes as spot
    const timeframeAnalyses: FuturesBuySellVolumeAnalysis[] = [];
    let totalScore = 0;
    let bullishCount = 0;
    let bearishCount = 0;
    let strongestTimeframe: string | null = null;
    let strongestScore = 0;

    for (const timeframe of timeframes) {
      try {
        const analysis = await this.analyzeBuySellVolumeForTimeframe(symbol, timeframe);
        if (analysis) {
          timeframeAnalyses.push(analysis);

          // Calculate score contribution (0-1 scale)
          const scoreContribution = Math.max(analysis.buyVolumeRatio, analysis.sellVolumeRatio) - 1;
          totalScore += scoreContribution;

          // Track strongest signal
          if (scoreContribution > strongestScore) {
            strongestScore = scoreContribution;
            strongestTimeframe = timeframe;
          }

          // Count directional signals
          if (analysis.signal === 'BULLISH') bullishCount++;
          else if (analysis.signal === 'BEARISH') bearishCount++;
        }
      } catch (error) {
        logError(`Error analyzing futures volume for ${symbol} ${timeframe}`, error as Error);
      }
    }

    // Determine overall signal based on timeframe consensus
    let overallSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
    if (bullishCount > bearishCount && bullishCount >= 2) {
      overallSignal = 'BULLISH';
    } else if (bearishCount > bullishCount && bearishCount >= 2) {
      overallSignal = 'BEARISH';
    }

    // Normalize score to 0-1 range
    const normalizedScore = Math.min(1, totalScore / timeframes.length);

    return {
      symbol,
      timeframes: timeframeAnalyses,
      overallSignal,
      strongestTimeframe,
      score: normalizedScore,
      detected: timeframeAnalyses.length > 0 && normalizedScore > 0.1
    } as FuturesMultiTimeframeVolumeAnalysis & { detected: boolean };
  }

  // Analyze buy/sell volume for specific timeframe (adapted for futures)
  private async analyzeBuySellVolumeForTimeframe(symbol: string, timeframe: string): Promise<FuturesBuySellVolumeAnalysis | null> {
    try {
      // Get futures OHLCV data for the timeframe
      const ohlcvData = await this.futuresService.getOHLCV(symbol, timeframe, 75);
      if (!ohlcvData || ohlcvData.length < 75) {
        return null;
      }

      // Use last 75 periods for analysis (same as spot)
      const recentCandles = ohlcvData.slice(-75);
      const currentCandle = recentCandles[recentCandles.length - 1];
      const historicalCandles = recentCandles.slice(0, -1);

      // Extract volume data from OHLCV format [timestamp, open, high, low, close, volume]
      const currentVolume = currentCandle[5]; // Volume is at index 5

      // For futures, estimate buy/sell volumes using price action
      // If close > open, assume more buying pressure
      const currentOpen = currentCandle[1];
      const currentClose = currentCandle[4];
      const bullishCandle = currentClose > currentOpen;

      // Estimate buy/sell split based on price action (simplified approach)
      const currentBuyVolume = bullishCandle ? currentVolume * 0.6 : currentVolume * 0.4;
      const currentSellVolume = currentVolume - currentBuyVolume;

      // Calculate historical averages
      let totalBuyVolume = 0;
      let totalSellVolume = 0;

      historicalCandles.forEach((candle: any) => {
        const volume = candle[5];
        const open = candle[1];
        const close = candle[4];
        const isBullish = close > open;

        const buyVolume = isBullish ? volume * 0.6 : volume * 0.4;
        const sellVolume = volume - buyVolume;

        totalBuyVolume += buyVolume;
        totalSellVolume += sellVolume;
      });

      const avgBuyVolume = totalBuyVolume / historicalCandles.length;
      const avgSellVolume = totalSellVolume / historicalCandles.length;

      // Calculate ratios
      const buyVolumeRatio = avgBuyVolume > 0 ? currentBuyVolume / avgBuyVolume : 0;
      const sellVolumeRatio = avgSellVolume > 0 ? currentSellVolume / avgSellVolume : 0;

      // Determine spikes (same thresholds as spot)
      const buyVolumeSpike = buyVolumeRatio > 2.0;
      const sellVolumeSpike = sellVolumeRatio > 2.0;

      // Determine dominant direction
      let dominantDirection: 'BUY' | 'SELL' | 'NEUTRAL' = 'NEUTRAL';
      let signal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';

      if (buyVolumeSpike && buyVolumeRatio > sellVolumeRatio * 1.5) {
        dominantDirection = 'BUY';
        signal = 'BULLISH';
      } else if (sellVolumeSpike && sellVolumeRatio > buyVolumeRatio * 1.5) {
        dominantDirection = 'SELL';
        signal = 'BEARISH';
      }

      return {
        timeframe,
        currentBuyVolume,
        currentSellVolume,
        avgBuyVolume,
        avgSellVolume,
        buyVolumeRatio,
        sellVolumeRatio,
        buyVolumeSpike,
        sellVolumeSpike,
        dominantDirection,
        signal
      };

    } catch (error) {
      logError(`Error analyzing futures buy/sell volume for ${symbol} ${timeframe}`, error as Error);
      return null;
    }
  }

  // RSI momentum analysis (identical to spot)
  private async analyzeRSIMomentum(symbol: string): Promise<FuturesRSIAnalysis & { detected: boolean } | null> {
    try {
      // Get futures technical indicators
      const indicators = await this.technicalAnalysis.calculateIndicators('binance', symbol, '15m');
      if (!indicators || typeof indicators.rsi !== 'number') {
        logDebug(`No RSI data available for futures ${symbol} - insufficient data`);
        return null;
      }

      // Get historical RSI values for momentum calculation
      if (!this.rsiHistory.has(symbol)) {
        this.rsiHistory.set(symbol, []);
      }

      const rsiHistory = this.rsiHistory.get(symbol)!;
      rsiHistory.push(indicators.rsi);

      // Keep only last 10 values
      if (rsiHistory.length > 10) {
        rsiHistory.shift();
      }

      // Need at least 2 values for momentum calculation
      if (rsiHistory.length < 2) {
        return null;
      }

      const currentRSI = rsiHistory[rsiHistory.length - 1];
      const previousRSI = rsiHistory[rsiHistory.length - 2];
      const velocity = currentRSI - previousRSI;

      // Determine momentum direction (same logic as spot)
      let momentum: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
      let detected = false;

      // RSI momentum detection (same thresholds as spot)
      if (currentRSI < 30 && velocity > 2) {
        momentum = 'BULLISH'; // Oversold with upward momentum
        detected = true;
      } else if (currentRSI > 70 && velocity < -2) {
        momentum = 'BEARISH'; // Overbought with downward momentum
        detected = true;
      } else if (Math.abs(velocity) > 3) {
        momentum = velocity > 0 ? 'BULLISH' : 'BEARISH'; // Strong momentum
        detected = true;
      }

      return {
        currentRSI,
        previousRSI,
        velocity,
        momentum,
        detected
      };

    } catch (error) {
      logError(`Error analyzing futures RSI momentum for ${symbol}`, error as Error);
      return null;
    }
  }

  // EMA convergence analysis (identical to spot)
  private async analyzeEMAConvergence(symbol: string): Promise<FuturesEMAAnalysis | null> {
    try {
      // Get futures technical indicators
      const indicators = await this.technicalAnalysis.calculateIndicators('binance', symbol, '15m');
      if (!indicators || typeof indicators.ema20 !== 'number' || typeof indicators.ema50 !== 'number') {
        logDebug(`No EMA data available for futures ${symbol} - insufficient data or system warming up`);
        return null;
      }

      const ema20 = indicators.ema20;
      const ema50 = indicators.ema50;
      const gap = ema20 - ema50;
      const gapPercent = Math.abs(gap) / ema50 * 100;

      // Calculate momentum (same logic as spot)
      const momentum = gap / ema50;

      // Convergence detection (same thresholds as spot)
      const convergence = gapPercent < 0.5 && Math.abs(momentum) > 0;

      return {
        ema20,
        ema50,
        gap,
        gapPercent,
        momentum,
        convergence
      };

    } catch (error) {
      logError(`Error analyzing futures EMA convergence for ${symbol}`, error as Error);
      return null;
    }
  }

  // Phase 2: Order Flow Analysis (Max 35 points total - identical to spot)
  private async analyzePhase2(symbol: string) {
    const results = {
      bidAskImbalance: null as any,
      priceAction: null as any,
      score: 0,
      subComponentScores: {
        bidAskImbalance: 0,
        priceAction: 0
      },
      overallSignal: 'NEUTRAL' as 'BULLISH' | 'BEARISH' | 'NEUTRAL'
    };

    let bidAskScore = 0;
    let priceActionScore = 0;
    let bidAskSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
    let priceActionSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';

    try {
      // Sub-component 1: Bid/Ask Imbalance Analysis (17.5 points max)
      const bidAskAnalysis = await this.analyzeBidAskImbalance(symbol);
      if (bidAskAnalysis && bidAskAnalysis.detected) {
        results.bidAskImbalance = bidAskAnalysis;
        bidAskScore = Math.min(17.5, bidAskAnalysis.score * 17.5);
        bidAskSignal = bidAskAnalysis.signal;
      }

      // Sub-component 2: Price Action Analysis (17.5 points max)
      const priceActionAnalysis = await this.analyzePriceAction(symbol);
      if (priceActionAnalysis && priceActionAnalysis.detected) {
        results.priceAction = priceActionAnalysis;
        priceActionScore = Math.min(17.5, priceActionAnalysis.score * 17.5);
        priceActionSignal = priceActionAnalysis.signal;
      }

      // Store sub-component scores
      results.subComponentScores.bidAskImbalance = bidAskScore;
      results.subComponentScores.priceAction = priceActionScore;

      // Calculate directional score using the same logic as spot
      let directionalScore = 0;

      // Apply directional scoring for bid/ask imbalance
      if (bidAskSignal === 'BULLISH') {
        directionalScore += bidAskScore;
      } else if (bidAskSignal === 'BEARISH') {
        directionalScore -= bidAskScore;
      }

      // Apply directional scoring for price action
      if (priceActionSignal === 'BULLISH') {
        directionalScore += priceActionScore;
      } else if (priceActionSignal === 'BEARISH') {
        directionalScore -= priceActionScore;
      }

      // Determine overall Phase 2 signal based on net directional score
      if (directionalScore > 0) {
        results.overallSignal = 'BULLISH';
      } else if (directionalScore < 0) {
        results.overallSignal = 'BEARISH';
      } else {
        results.overallSignal = 'NEUTRAL';
      }

      // Store absolute score for legacy compatibility
      results.score = Math.abs(directionalScore);

    } catch (error) {
      logError(`Error in futures Phase 2 analysis for ${symbol}`, error as Error);
    }

    logInfo(`Futures Phase 2 Analysis for ${symbol}:`, {
      bidAskImbalance: results.bidAskImbalance?.detected || false,
      priceAction: results.priceAction?.detected || false,
      overallSignal: results.overallSignal,
      score: results.score
    });

    return results;
  }

  // Bid/Ask imbalance analysis (simplified for futures without order book)
  private async analyzeBidAskImbalance(symbol: string): Promise<any> {
    try {
      // Since futures order book is not available, use price action as proxy
      const ohlcvData = await this.futuresService.getOHLCV(symbol, '1m', 20);
      if (!ohlcvData || ohlcvData.length < 20) {
        return { detected: false, score: 0, signal: 'NEUTRAL' };
      }

      const recentCandles = ohlcvData.slice(-10); // Last 10 minutes
      let bullishCandles = 0;
      let bearishCandles = 0;
      let totalVolume = 0;
      let bullishVolume = 0;
      let bearishVolume = 0;

      // Analyze recent price action to estimate order flow
      recentCandles.forEach((candle: any) => {
        const [timestamp, open, high, low, close, volume] = candle;
        const isBullish = close > open;

        totalVolume += volume;
        if (isBullish) {
          bullishCandles++;
          bullishVolume += volume;
        } else {
          bearishCandles++;
          bearishVolume += volume;
        }
      });

      if (totalVolume === 0) {
        return { detected: false, score: 0, signal: 'NEUTRAL' };
      }

      // Calculate imbalance based on volume distribution
      const bullishRatio = bullishVolume / totalVolume;
      const bearishRatio = bearishVolume / totalVolume;
      const imbalance = Math.abs(bullishRatio - bearishRatio);

      // Determine signal direction and strength
      let signal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
      let detected = false;
      let score = 0;

      if (imbalance > 0.2) { // 20% imbalance threshold for futures
        detected = true;
        score = Math.min(1, imbalance * 1.5); // Normalize to 0-1
        signal = bullishRatio > bearishRatio ? 'BULLISH' : 'BEARISH';
      }

      return {
        detected,
        score,
        signal,
        bidRatio: bullishRatio, // Use bullish ratio as proxy for bid ratio
        askRatio: bearishRatio, // Use bearish ratio as proxy for ask ratio
        imbalance,
        totalBidVolume: bullishVolume,
        totalAskVolume: bearishVolume
      };

    } catch (error) {
      logError(`Error analyzing futures bid/ask imbalance for ${symbol}`, error as Error);
      return { detected: false, score: 0, signal: 'NEUTRAL' };
    }
  }

  // Price action analysis (adapted for futures OHLCV data)
  private async analyzePriceAction(symbol: string): Promise<any> {
    try {
      // Get recent futures OHLCV data
      const ohlcvData = await this.futuresService.getOHLCV(symbol, '5m', 20);
      if (!ohlcvData || ohlcvData.length < 20) {
        return { detected: false, score: 0, signal: 'NEUTRAL' };
      }

      const recentCandles = ohlcvData.slice(-20); // Last 20 periods
      const currentCandle = recentCandles[recentCandles.length - 1];
      const previousCandles = recentCandles.slice(0, -1);

      // Extract OHLCV data [timestamp, open, high, low, close, volume]
      const [timestamp, currentOpen, currentHigh, currentLow, currentClose, currentVolume] = currentCandle;

      // Calculate average volume for comparison
      const avgVolume = previousCandles.reduce((sum: number, candle: any) => sum + candle[5], 0) / previousCandles.length;

      // Price action signals (same logic as spot)
      let score = 0;
      let signal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
      let detected = false;

      // Strong candle with volume
      const bodySize = Math.abs(currentClose - currentOpen);
      const candleRange = currentHigh - currentLow;
      const bodyRatio = candleRange > 0 ? bodySize / candleRange : 0;

      if (bodyRatio > 0.7 && currentVolume > avgVolume * 1.5) {
        detected = true;
        score = Math.min(1, bodyRatio * (currentVolume / avgVolume) / 2);
        signal = currentClose > currentOpen ? 'BULLISH' : 'BEARISH';
      }

      return {
        detected,
        score,
        signal,
        bodyRatio,
        volumeRatio: currentVolume / avgVolume,
        priceChange: (currentClose - currentOpen) / currentOpen
      };

    } catch (error) {
      logError(`Error analyzing futures price action for ${symbol}`, error as Error);
      return { detected: false, score: 0, signal: 'NEUTRAL' };
    }

  }

  // Phase 3: Whale Activity Detection (Max 40 points total - identical to spot)
  private async analyzePhase3(symbol: string) {
    const results = {
      whaleActivity: null as any,
      score: 0,
      overallSignal: 'NEUTRAL' as 'BULLISH' | 'BEARISH' | 'NEUTRAL'
    };

    try {
      // Use comprehensive Binance whale detection (same service as spot)
      const whaleAnalysis = await this.whaleDetectionService.detectWhaleActivity(symbol);

      if (whaleAnalysis && whaleAnalysis.detected) {
        results.whaleActivity = whaleAnalysis;

        // Calculate score based on whale confidence (same logic as spot)
        const baseScore = whaleAnalysis.confidence * 40; // Max 40 points
        results.score = Math.min(40, baseScore);

        // Determine signal direction based on transfer direction (adapted for whale detection service)
        if (whaleAnalysis.transferDirection === 'DISTRIBUTION') {
          results.overallSignal = 'BEARISH'; // Distribution = potential sell pressure
        } else if (whaleAnalysis.transferDirection === 'ACCUMULATION') {
          results.overallSignal = 'BULLISH'; // Accumulation = potential buy pressure
        } else {
          results.overallSignal = 'NEUTRAL'; // Unknown, large transfer, or neutral direction
        }
      }

    } catch (error) {
      logError(`Error in futures Phase 3 analysis for ${symbol}`, error as Error);
    }

    logInfo(`Futures Phase 3 Analysis for ${symbol}:`, {
      whaleActivity: results.whaleActivity?.detected || false,
      confidence: results.whaleActivity?.confidence || 0,
      transferDirection: results.whaleActivity?.transferDirection || 'UNKNOWN',
      overallSignal: results.overallSignal,
      estimatedValue: results.whaleActivity?.estimatedValue || 0,
      score: results.score
    });

    return results;
  }

  // Calculate overall alert from all phases (identical to spot)
  private calculateOverallAlert(
    symbol: string,
    currentPrice: number,
    volume24h: number,
    priceChange24h: number,
    phase1: any,
    phase2: any,
    phase3: any
  ): FuturesEarlyWarningAlert | null {
    try {
      // Collect all triggered signals (same logic as spot)
      const triggeredBy: string[] = [];

      // Phase 1 triggers
      if (phase1.multiTimeframeVolumeAnalysis?.detected) {
        triggeredBy.push(`Multi-timeframe volume (${phase1.multiTimeframeVolumeAnalysis.overallSignal})`);
      }
      if (phase1.rsiMomentum?.detected) {
        triggeredBy.push(`RSI momentum (${phase1.rsiMomentum.momentum})`);
      }
      if (phase1.emaConvergence?.detected) {
        triggeredBy.push('EMA convergence');
      }

      // Phase 2 triggers
      if (phase2.bidAskImbalance?.detected) {
        triggeredBy.push(`Order flow imbalance (${phase2.bidAskImbalance.signal})`);
      }
      if (phase2.priceAction?.detected) {
        triggeredBy.push(`Price action (${phase2.priceAction.signal})`);
      }

      // Phase 3 triggers
      if (phase3.whaleActivity?.detected) {
        triggeredBy.push(`Whale activity (${phase3.whaleActivity.transferDirection})`);
      }

      // Must have at least one trigger to generate alert (same as spot)
      if (triggeredBy.length === 0) {
        return null;
      }

      // Calculate directional confidence using the same logic as spot
      let netDirectionalScore = 0;

      // Phase 1 contribution (max ±25 points)
      let phase1Contribution = 0;
      if (phase1.overallSignal && phase1.overallSignal !== 'NEUTRAL') {
        const phase1Score = Math.min(phase1.score, 25);
        phase1Contribution = phase1.overallSignal === 'BULLISH'
          ? phase1Score
          : -phase1Score;
      }

      // Phase 2 contribution (max ±35 points)
      let phase2Contribution = 0;
      if (phase2.overallSignal && phase2.overallSignal !== 'NEUTRAL') {
        const phase2Score = Math.min(phase2.score, 35);
        phase2Contribution = phase2.overallSignal === 'BULLISH'
          ? phase2Score
          : -phase2Score;
      }

      // Phase 3 contribution (max ±40 points)
      let phase3Contribution = 0;
      if (phase3.overallSignal && phase3.overallSignal !== 'NEUTRAL') {
        const phase3Score = Math.min(phase3.score, 40);
        phase3Contribution = phase3.overallSignal === 'BULLISH'
          ? phase3Score
          : -phase3Score;
      }

      // Calculate net directional score
      netDirectionalScore = phase1Contribution + phase2Contribution + phase3Contribution;

      // Determine alert type and confidence (same logic as spot)
      let alertType: 'PUMP_LIKELY' | 'DUMP_LIKELY' | 'NEUTRAL' = 'NEUTRAL';
      let confidence = 0;

      if (netDirectionalScore > 0) {
        alertType = 'PUMP_LIKELY';
        confidence = Math.min(100, (Math.abs(netDirectionalScore) / 100) * 100);
      } else if (netDirectionalScore < 0) {
        alertType = 'DUMP_LIKELY';
        confidence = Math.min(100, (Math.abs(netDirectionalScore) / 100) * 100);
      }

      // Note: Confidence threshold is checked in the route against rule.minConfidence
      // No hardcoded minimum here - let the rule configuration determine the threshold

      // Calculate time estimates based on confidence (same logic as spot)
      let timeEstimateMin = 15; // 15 minutes minimum
      let timeEstimateMax = 120; // 2 hours maximum

      if (confidence > 70) {
        timeEstimateMin = 5;
        timeEstimateMax = 30;
      } else if (confidence > 50) {
        timeEstimateMin = 10;
        timeEstimateMax = 60;
      } else if (confidence > 30) {
        timeEstimateMin = 15;
        timeEstimateMax = 90;
      }

      return {
        symbol,
        alertType,
        confidence,
        timeEstimateMin,
        timeEstimateMax,
        triggeredBy,
        currentPrice,
        volume24h,
        priceChange24h,

        // Phase data (identical structure to spot)
        volumeSpike: phase1.volumeSpike,
        multiTimeframeVolumeAnalysis: phase1.multiTimeframeVolumeAnalysis,
        rsiMomentum: phase1.rsiMomentum,
        emaConvergence: phase1.emaConvergence,
        bidAskImbalance: phase2.bidAskImbalance,
        priceAction: phase2.priceAction,
        whaleActivity: phase3.whaleActivity,

        // Scores (identical to spot)
        phase1Score: Math.abs(phase1Contribution),
        phase2Score: Math.abs(phase2Contribution),
        phase3Score: Math.abs(phase3Contribution)
      };

    } catch (error) {
      logError(`Error calculating futures overall alert for ${symbol}`, error as Error);
      return null;
    }
  }

  // Public method to check if system is ready for analysis
  public isReady(): boolean {
    return this.isSystemReady;
  }

  // Get system status information
  public getSystemStatus() {
    const now = Date.now();
    const timeSinceStart = now - this.systemStartTime;

    return {
      isReady: this.isSystemReady,
      timeSinceStart: Math.round(timeSinceStart / 1000), // seconds
      minimumWarmupTime: this.MINIMUM_WARMUP_TIME_MS / 1000, // seconds
      minimumDataThreshold: this.MINIMUM_DATA_THRESHOLD
    };
  }
}
