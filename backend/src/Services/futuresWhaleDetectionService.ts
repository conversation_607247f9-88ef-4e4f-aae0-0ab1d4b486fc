import { logInfo, logError, logDebug } from '../utils/logger';
import { BinanceFuturesService } from './binanceFuturesService';

// Interfaces matching spot whale detection service exactly
interface FuturesWhaleTransfer {
  symbol: string;
  amount: number;
  estimatedValue: number;
  transferDirection: 'ACCUMULATION' | 'DISTRIBUTION' | 'LARGE_TRANSFER' | 'UNKNOWN';
  confidence: number;
  timestamp: number;
  source: string;
}

interface FuturesWhaleActivity {
  detected: boolean;
  confidence: number;
  transferDirection: 'ACCUMULATION' | 'DISTRIBUTION' | 'LARGE_TRANSFER' | 'UNKNOWN';
  estimatedValue: number;
  transfers: FuturesWhaleTransfer[];
  analysis: {
    largeOrderDetection: any;
    positionSizeAnalysis: any;
    fundingRateAnalysis: any;
    openInterestAnalysis: any;
  };
}

export class FuturesWhaleDetectionService {
  private futuresService: BinanceFuturesService;
  
  // Tracking maps for futures-specific whale activity
  private positionTracker: Map<string, any[]> = new Map();
  private fundingRateHistory: Map<string, number[]> = new Map();
  private openInterestHistory: Map<string, number[]> = new Map();
  private lastAnalysis: Map<string, number> = new Map();
  
  // Thresholds for futures whale detection (adapted for leverage)
  private readonly LARGE_POSITION_THRESHOLD = 1000000; // $1M USD (lower due to leverage)
  private readonly FUNDING_RATE_ANOMALY_THRESHOLD = 0.01; // 1% funding rate spike
  private readonly OPEN_INTEREST_SPIKE_THRESHOLD = 1.5; // 50% OI increase
  private readonly ANALYSIS_COOLDOWN_MS = 60 * 1000; // 1 minute cooldown

  constructor(futuresService: BinanceFuturesService) {
    this.futuresService = futuresService;
    logInfo('🐋 Futures Whale Detection Service initialized');
  }

  // Main whale activity detection method (identical structure to spot)
  async detectWhaleActivity(symbol: string): Promise<FuturesWhaleActivity | null> {
    try {
      // Check cooldown to prevent excessive analysis
      const now = Date.now();
      const lastAnalysisTime = this.lastAnalysis.get(symbol) || 0;
      if (now - lastAnalysisTime < this.ANALYSIS_COOLDOWN_MS) {
        return null;
      }
      this.lastAnalysis.set(symbol, now);

      logDebug(`🐋 Analyzing futures whale activity for ${symbol}`);

      // Get current futures ticker data
      const ticker = this.futuresService.getCachedPrice(symbol);
      if (!ticker) {
        return null;
      }

      const currentPrice = parseFloat(ticker.price);
      const volume24h = parseFloat(ticker.volume);

      // Futures-specific whale detection components
      const largeOrderDetection = await this.detectLargeOrders(symbol, currentPrice);
      const positionSizeAnalysis = await this.analyzePositionSizes(symbol, currentPrice, volume24h);
      const fundingRateAnalysis = await this.analyzeFundingRates(symbol);
      const openInterestAnalysis = await this.analyzeOpenInterest(symbol);

      // Calculate overall confidence based on all components
      let totalConfidence = 0;
      let detectedTransfers: FuturesWhaleTransfer[] = [];
      let transferDirection: 'ACCUMULATION' | 'DISTRIBUTION' | 'LARGE_TRANSFER' | 'UNKNOWN' = 'UNKNOWN';
      let estimatedValue = 0;

      // Large order detection (25% weight)
      if (largeOrderDetection.detected) {
        totalConfidence += largeOrderDetection.confidence * 0.25;
        detectedTransfers.push(...largeOrderDetection.transfers);
        estimatedValue += largeOrderDetection.estimatedValue;
      }

      // Position size analysis (25% weight)
      if (positionSizeAnalysis.detected) {
        totalConfidence += positionSizeAnalysis.confidence * 0.25;
        estimatedValue += positionSizeAnalysis.estimatedValue;
      }

      // Funding rate analysis (25% weight)
      if (fundingRateAnalysis.detected) {
        totalConfidence += fundingRateAnalysis.confidence * 0.25;
      }

      // Open interest analysis (25% weight)
      if (openInterestAnalysis.detected) {
        totalConfidence += openInterestAnalysis.confidence * 0.25;
      }

      // Determine transfer direction based on analysis
      if (positionSizeAnalysis.direction === 'LONG_ACCUMULATION' || fundingRateAnalysis.direction === 'BULLISH') {
        transferDirection = 'ACCUMULATION';
      } else if (positionSizeAnalysis.direction === 'SHORT_ACCUMULATION' || fundingRateAnalysis.direction === 'BEARISH') {
        transferDirection = 'DISTRIBUTION';
      } else if (estimatedValue > this.LARGE_POSITION_THRESHOLD) {
        transferDirection = 'LARGE_TRANSFER';
      }

      // Must have minimum confidence to be considered detected
      const detected = totalConfidence > 0.15; // 15% minimum confidence

      if (detected) {
        logInfo(`🐋 Futures whale activity detected for ${symbol}:`, {
          confidence: totalConfidence,
          transferDirection,
          estimatedValue,
          transferCount: detectedTransfers.length
        });
      }

      return {
        detected,
        confidence: totalConfidence,
        transferDirection,
        estimatedValue,
        transfers: detectedTransfers,
        analysis: {
          largeOrderDetection,
          positionSizeAnalysis,
          fundingRateAnalysis,
          openInterestAnalysis
        }
      };

    } catch (error) {
      logError(`Error detecting futures whale activity for ${symbol}`, error as Error);
      return null;
    }
  }

  // Detect large orders using volume analysis (futures-specific)
  private async detectLargeOrders(symbol: string, currentPrice: number): Promise<any> {
    try {
      // Get recent OHLCV data to analyze for large orders
      const ohlcvData = await this.futuresService.getOHLCV(symbol, '1m', 20);
      if (!ohlcvData || ohlcvData.length < 10) {
        return { detected: false, confidence: 0, transfers: [], estimatedValue: 0 };
      }

      const recentCandles = ohlcvData.slice(-10); // Last 10 minutes
      const transfers: FuturesWhaleTransfer[] = [];
      let totalEstimatedValue = 0;
      let maxConfidence = 0;

      // Analyze each candle for unusual volume spikes
      recentCandles.forEach((candle: any, index: number) => {
        const [timestamp, open, high, low, close, volume] = candle;
        
        // Calculate average volume from previous candles
        const previousCandles = recentCandles.slice(0, index);
        if (previousCandles.length < 3) return;
        
        const avgVolume = previousCandles.reduce((sum: number, c: any) => sum + c[5], 0) / previousCandles.length;
        const volumeRatio = volume / avgVolume;
        
        // Detect volume spikes (futures threshold: 3x average)
        if (volumeRatio > 3.0) {
          const estimatedValue = volume * currentPrice;
          const confidence = Math.min(1, volumeRatio / 5); // Normalize to 0-1
          
          if (estimatedValue > this.LARGE_POSITION_THRESHOLD * 0.5) { // 50% of threshold
            transfers.push({
              symbol,
              amount: volume,
              estimatedValue,
              transferDirection: close > open ? 'ACCUMULATION' : 'DISTRIBUTION',
              confidence,
              timestamp,
              source: 'LARGE_ORDER_DETECTION'
            });
            
            totalEstimatedValue += estimatedValue;
            maxConfidence = Math.max(maxConfidence, confidence);
          }
        }
      });

      return {
        detected: transfers.length > 0,
        confidence: maxConfidence,
        transfers,
        estimatedValue: totalEstimatedValue
      };

    } catch (error) {
      logError(`Error detecting large futures orders for ${symbol}`, error as Error);
      return { detected: false, confidence: 0, transfers: [], estimatedValue: 0 };
    }
  }

  // Analyze position sizes using volume and price action (futures-specific)
  private async analyzePositionSizes(symbol: string, currentPrice: number, volume24h: number): Promise<any> {
    try {
      // Get longer timeframe data for position analysis
      const ohlcvData = await this.futuresService.getOHLCV(symbol, '15m', 24); // Last 6 hours
      if (!ohlcvData || ohlcvData.length < 12) {
        return { detected: false, confidence: 0, direction: 'UNKNOWN', estimatedValue: 0 };
      }

      const recentCandles = ohlcvData.slice(-12); // Last 3 hours
      let bullishVolume = 0;
      let bearishVolume = 0;
      let totalVolume = 0;

      // Analyze directional volume accumulation
      recentCandles.forEach((candle: any) => {
        const [timestamp, open, high, low, close, volume] = candle;
        totalVolume += volume;
        
        if (close > open) {
          bullishVolume += volume;
        } else {
          bearishVolume += volume;
        }
      });

      if (totalVolume === 0) {
        return { detected: false, confidence: 0, direction: 'UNKNOWN', estimatedValue: 0 };
      }

      // Calculate directional bias
      const bullishRatio = bullishVolume / totalVolume;
      const bearishRatio = bearishVolume / totalVolume;
      const bias = Math.abs(bullishRatio - bearishRatio);
      
      // Detect significant directional accumulation
      let direction: 'LONG_ACCUMULATION' | 'SHORT_ACCUMULATION' | 'UNKNOWN' = 'UNKNOWN';
      let detected = false;
      let confidence = 0;
      
      if (bias > 0.3) { // 30% directional bias threshold
        detected = true;
        confidence = Math.min(1, bias * 1.5);
        direction = bullishRatio > bearishRatio ? 'LONG_ACCUMULATION' : 'SHORT_ACCUMULATION';
      }

      const estimatedValue = totalVolume * currentPrice;

      return {
        detected,
        confidence,
        direction,
        estimatedValue,
        bullishRatio,
        bearishRatio,
        bias
      };

    } catch (error) {
      logError(`Error analyzing futures position sizes for ${symbol}`, error as Error);
      return { detected: false, confidence: 0, direction: 'UNKNOWN', estimatedValue: 0 };
    }
  }

  // Analyze funding rates for whale activity (futures-specific)
  private async analyzeFundingRates(symbol: string): Promise<any> {
    try {
      // For now, return a simplified analysis
      // In a full implementation, this would fetch actual funding rate data
      return {
        detected: false,
        confidence: 0,
        direction: 'UNKNOWN',
        currentRate: 0,
        rateChange: 0,
        anomalyDetected: false
      };

    } catch (error) {
      logError(`Error analyzing futures funding rates for ${symbol}`, error as Error);
      return { detected: false, confidence: 0, direction: 'UNKNOWN' };
    }
  }

  // Analyze open interest changes (futures-specific)
  private async analyzeOpenInterest(symbol: string): Promise<any> {
    try {
      // For now, return a simplified analysis
      // In a full implementation, this would fetch actual open interest data
      return {
        detected: false,
        confidence: 0,
        currentOI: 0,
        oiChange: 0,
        spikeDetected: false
      };

    } catch (error) {
      logError(`Error analyzing futures open interest for ${symbol}`, error as Error);
      return { detected: false, confidence: 0 };
    }
  }

  // Cleanup old data to prevent memory leaks
  cleanup(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    // Clean up old analysis times
    for (const [symbol, timestamp] of this.lastAnalysis.entries()) {
      if (now - timestamp > maxAge) {
        this.lastAnalysis.delete(symbol);
      }
    }

    // Clean up tracking data
    this.positionTracker.clear();
    this.fundingRateHistory.clear();
    this.openInterestHistory.clear();

    logDebug('Futures whale detection service cleanup completed');
  }
}
