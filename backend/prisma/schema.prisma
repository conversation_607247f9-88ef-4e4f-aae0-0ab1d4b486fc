generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model AdminSettings {
  id           String   @id @default(cuid())
  settingKey   String   @unique
  settingValue String
  settingType  String
  description  String?
  category     String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("admin_settings")
}

model NotificationRule {
  id                     String         @id @default(cuid())
  name                   String
  description            String?
  isActive               Boolean        @default(true)
  minConfidence          Float?
  minStrength            Float?
  requiredTimeframes     Int?
  requiredSignalType     String?
  advancedConditions     Json?
  enableSound            Boolean        @default(true)
  enableVisual           Boolean        @default(true)
  priority               String         @default("MEDIUM")
  createdAt              DateTime       @default(now())
  updatedAt              DateTime       @updatedAt
  specificTimeframes     Json?
  indicatorRequirements  Json?
  marketType             String?        @default("spot")
  triggeredNotifications Notification[]

  @@map("notification_rules")
}

model SignalHistory {
  id                  String         @id @default(cuid())
  symbol              String
  exchange            String         @default("binance")
  timeframe           String
  signal              String
  confidence          Float
  strength            Float
  currentPrice        Float
  technicalIndicators Json?
  chartPatterns       Json?
  candlestickPatterns Json?
  reasoning           Json?
  generatedAt         DateTime       @default(now())
  processingTimeMs    Int?
  fundingRate         Float?
  marketType          String         @default("spot")
  nextFundingTime     DateTime?
  openInterest        Float?
  notifications       Notification[]

  @@index([marketType, symbol, generatedAt])
  @@index([signal, confidence, generatedAt])
  @@index([symbol, timeframe, generatedAt])
  @@map("signal_history")
}

model Notification {
  id                  String            @id @default(cuid())
  title               String
  message             String
  type                String
  priority            String
  symbol              String?
  signal              String?
  confidence          Float?
  strength            Float?
  timeframe           String?
  hasVisual           Boolean           @default(true)
  isRead              Boolean           @default(false)
  ruleId              String?
  signalId            String?
  createdAt           DateTime          @default(now())
  readAt              DateTime?
  analysisReasoning   Json?
  candlestickPatterns Json?
  chartPatterns       Json?
  currentPrice        Float?
  exchange            String?
  technicalIndicators Json?
  triggeredTimeframes Json?
  fundingRate         Float?
  marketType          String?           @default("spot")
  openInterest        Float?
  rule                NotificationRule? @relation(fields: [ruleId], references: [id])
  signal_history      SignalHistory?    @relation(fields: [signalId], references: [id])

  @@index([createdAt, isRead])
  @@index([marketType, symbol, createdAt])
  @@index([symbol, signal, createdAt])
  @@index([type, priority, createdAt])
  @@map("notifications")
}

model EarlyWarningAlert {
  id                           String    @id @default(cuid())
  symbol                       String
  exchange                     String    @default("binance")
  alertType                    String
  confidence                   Float
  timeEstimateMin              Int
  timeEstimateMax              Int
  volumeSpike                  Json?
  rsiMomentum                  Json?
  emaConvergence               Json?
  bidAskImbalance              Json?
  priceAction                  Json?
  whaleActivity                Json?
  phase1Score                  Float     @default(0)
  phase2Score                  Float     @default(0)
  phase3Score                  Float     @default(0)
  triggeredBy                  Json
  currentPrice                 Float
  volume24h                    Float?
  priceChange24h               Float?
  isActive                     Boolean   @default(true)
  isResolved                   Boolean   @default(false)
  resolvedAt                   DateTime?
  actualOutcome                String?
  accuracyScore                Float?
  responseTime                 Int?
  createdAt                    DateTime  @default(now())
  updatedAt                    DateTime  @updatedAt
  multiTimeframeVolumeAnalysis Json?
  isRead                       Boolean   @default(false)
  readAt                       DateTime?
  whaleHybridAnalysis          Json?
  marketType                   String    @default("spot")

  @@index([isRead, createdAt])
  @@index([actualOutcome, accuracyScore])
  @@index([confidence, createdAt])
  @@index([isActive, isResolved, createdAt])
  @@index([symbol, alertType, createdAt])
  @@index([marketType, symbol, alertType, createdAt])
  @@map("early_warning_alerts")
}

model EarlyWarningAlertRule {
  id               String                     @id @default(cuid())
  name             String
  description      String?
  minConfidence    Float
  alertTypes       Json
  requiredPhases   Json?
  minPhaseScore    Float?
  minTimeEstimate  Int?
  maxTimeEstimate  Int?
  requiredTriggers Json?
  priority         String                     @default("MEDIUM")
  enableToast      Boolean                    @default(true)
  enableSound      Boolean                    @default(true)
  isActive         Boolean                    @default(true)
  lastTriggered    DateTime?
  triggerCount     Int                        @default(0)
  marketType       String                     @default("SPOT")
  createdAt        DateTime                   @default(now())
  updatedAt        DateTime                   @updatedAt
  triggeredAlerts  EarlyWarningAlertHistory[]

  @@index([isActive])
  @@index([minConfidence, isActive])
  @@index([priority, isActive])
  @@index([marketType, isActive])
  @@map("early_warning_alert_rules")
}

model EarlyWarningAlertHistory {
  id              String                @id @default(cuid())
  ruleId          String
  ruleName        String
  earlyWarningId  String
  symbol          String
  alertType       String
  confidence      Float
  timeEstimateMin Int
  timeEstimateMax Int
  phase1Score     Float
  phase2Score     Float
  phase3Score     Float
  triggeredBy     Json
  currentPrice    Float
  volume24h       Float?
  priceChange24h  Float?
  volumeSpike     Json?
  rsiMomentum     Json?
  emaConvergence  Json?
  bidAskImbalance Json?
  priceAction     Json?
  whaleActivity   Json?
  message         String
  priority        String
  marketType      String                @default("SPOT")
  toastShown      Boolean               @default(false)
  isRead          Boolean               @default(false)
  readAt          DateTime?
  triggeredAt     DateTime              @default(now())
  rule            EarlyWarningAlertRule @relation(fields: [ruleId], references: [id], onDelete: Cascade)

  @@index([symbol, triggeredAt])
  @@index([alertType, confidence, triggeredAt])
  @@index([isRead, triggeredAt])
  @@index([ruleId, triggeredAt])
  @@index([marketType, symbol])
  @@map("early_warning_alert_history")
}
