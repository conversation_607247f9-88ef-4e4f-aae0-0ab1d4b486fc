-- Add marketType support to early warning alert rules and alerts tables

-- Add marketType column to early_warning_alert_rules table
ALTER TABLE "early_warning_alert_rules" ADD COLUMN "marketType" TEXT NOT NULL DEFAULT 'SPOT';

-- Add marketType column to early_warning_alerts table
ALTER TABLE "early_warning_alerts" ADD COLUMN "marketType" TEXT NOT NULL DEFAULT 'SPOT';

-- Add marketType column to early_warning_alert_history table
ALTER TABLE "early_warning_alert_history" ADD COLUMN "marketType" TEXT NOT NULL DEFAULT 'SPOT';

-- Add check constraints for market type values
ALTER TABLE "early_warning_alert_rules" ADD CONSTRAINT "early_warning_alert_rules_marketType_check" CHECK ("marketType" IN ('SPOT', 'FUTURES'));
ALTER TABLE "early_warning_alerts" ADD CONSTRAINT "early_warning_alerts_marketType_check" CHECK ("marketType" IN ('SPOT', 'FUTURES'));
ALTER TABLE "early_warning_alert_history" ADD CONSTRAINT "early_warning_alert_history_marketType_check" CHECK ("marketType" IN ('SPOT', 'FUTURES'));

-- Add indexes for better performance with market type
CREATE INDEX "early_warning_alert_rules_marketType_isActive_idx" ON "early_warning_alert_rules"("marketType", "isActive");
CREATE INDEX "early_warning_alerts_marketType_symbol_alertType_idx" ON "early_warning_alerts"("marketType", "symbol", "alertType", "createdAt");
CREATE INDEX "early_warning_alert_history_marketType_symbol_idx" ON "early_warning_alert_history"("marketType", "symbol", "triggeredAt");

-- Update existing data to have SPOT market type (already set as default, but ensuring consistency)
UPDATE "early_warning_alert_rules" SET "marketType" = 'SPOT' WHERE "marketType" IS NULL;
UPDATE "early_warning_alerts" SET "marketType" = 'SPOT' WHERE "marketType" IS NULL;
UPDATE "early_warning_alert_history" SET "marketType" = 'SPOT' WHERE "marketType" IS NULL;
